/* eslint-disable @typescript-eslint/no-explicit-any */
import { AppProvider } from "@/components/AppProvider";
import DashboardHeader from "@/components/Dashboard/DashboardHeader";
import Logout from "@/components/Dashboard/Logout";
import NavItem from "@/components/Dashboard/NavItem";
import LayoutContainer from "@/components/LayoutContainer";
import { Card } from "@/components/ui/card";
import { dashboardNav } from "@/constant/dashboard";
import { createClient } from "@/lib/server";
import { Maybe } from "@/types/common";
import { UsersType } from "@/types/db";

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  let userData: Maybe<UsersType> = null;
  if (user) {
    const { data, error: userError } = await supabase
      .from("users")
      .select(
        `
    id,
    email,
    credits_balance,
    credits_used_month,
    stripecustomerid,
    last_reset,
    plan_id,
    plan:plan_id (
      id,
      name,
      price_cents,
      daily_cap,
      monthly_cap,
      keyword_limit,
      prospected_filter,
      networks_limit,
      followers_filter
    )
  `
      )
      .eq("id", user.id)
      .single();

    if (!userError) {
      userData = {
        ...data,
        plan: Array.isArray(data.plan) ? data.plan[0] : data.plan,
      };
    }
  }

  return (
    <LayoutContainer>
      <AppProvider userData={userData}>
        <main className="min-h-screen dash-layout p-6 flex flex-col w-full gap-6">
          <DashboardHeader />
          <div className="flex flex-row gap-4 flex-1 w-full">
            <div className="hidden md:w-5/12 md:flex lg:w-3/12">
              <Card className="h-full w-full glassColor p-4">
                <div className="flex flex-col h-full">
                  <nav className="flex-1 space-y-2">
                    {dashboardNav.map((m, i) => (
                      <NavItem data={m} key={i} />
                    ))}
                  </nav>
                  <Logout />
                </div>
              </Card>
            </div>
            <div className="w-full md:w-7/12 lg:w-9/12">{children}</div>
          </div>
        </main>
      </AppProvider>
    </LayoutContainer>
  );
}
